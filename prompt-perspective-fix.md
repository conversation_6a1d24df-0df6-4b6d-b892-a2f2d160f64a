# Quick Replies Perspective Fix

## Issue Identified
The system prompt was causing the AI to treat Quick Replies as questions directed TO the AI, when they should actually be options that the USER would select to ask or explore further.

## Problem Examples

### Before (Incorrect):
Quick Replies were phrased as if the AI was asking itself:
- "What are the benefits of this approach?"
- "How does this compare to alternatives?"
- "What should be considered next?"

### After (Correct):
Quick Replies are now phrased as if the USER is asking:
- "Tell me about the benefits of this approach"
- "How does this compare to alternatives?"
- "What should I consider next?"

## Key Changes Made

### 1. Clarified Purpose
**Added explicit instruction**: "These are suggestions for what the USER might want to ask or explore next. Each option should be written as if the user is saying it themselves."

### 2. Updated Examples
**Before**: Generic "Option 1, Option 2" examples
**After**: Specific user-perspective examples:
- "Tell me more about [specific aspect]"
- "How do I [specific action]?"
- "What are the alternatives to [topic]?"
- "Can you explain [related concept]?"

### 3. Enhanced Guidelines
**New guidelines added**:
- Write each option from the USER'S perspective
- Use phrases like "Tell me about...", "How do I...", "What are...", "Can you explain...", "Show me..."
- Make each option a natural follow-up the user might genuinely want to explore
- Avoid making them sound like questions TO you - they are requests FROM the user

### 4. Clear Instruction Format
**Emphasized**: "where each option is phrased as something the USER would say"

## Expected Impact

### Better User Experience:
- Quick Replies will feel more natural and intuitive
- Users will understand that these are suggestions for their next questions
- Conversation flow will be more logical and user-centric

### Improved AI Responses:
- AI will generate more appropriate follow-up suggestions
- Options will be phrased from the user's perspective
- Better alignment between user intent and available options

## Implementation
- Updated system prompt in `server/ai-service.ts`
- Updated documentation in `prompt-optimized.md`
- Changes apply to all AI providers (OpenAI, Mistral, Google Gemini)
- No breaking changes to existing functionality

This fix ensures that Quick Replies serve their intended purpose as user-centric conversation continuation options rather than AI-centric questions.
