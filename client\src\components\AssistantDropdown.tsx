import { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SampleAssistant } from '@/assistants/SampleAssistant';
import { CareerAssistant } from '@/assistants/CareerAssistant';
import { HealthAssistant } from '@/assistants/HealthAssistant';
import { ReflectiveLifeCoachingAssistant } from '@/assistants/ReflectiveLifeCoachingAssistant';
import type { Assistant } from '../../../shared/assistants/types';

// Import all assistants
const assistants: Assistant[] = [
  SampleAssistant,
  ReflectiveLifeCoachingAssistant,
  CareerAssistant,
  HealthAssistant
];

interface AssistantDropdownProps {
  selectedAssistantId?: string;
  onAssistantChange: (assistant: Assistant | null) => void;
}

export function AssistantDropdown({ selectedAssistantId, onAssistantChange }: AssistantDropdownProps) {
  const [selectedId, setSelectedId] = useState<string>(selectedAssistantId || 'none');

  const handleValueChange = (value: string) => {
    setSelectedId(value);
    
    if (value === 'none') {
      onAssistantChange(null);
    } else {
      const selectedAssistant = assistants.find(assistant => assistant.id === value);
      onAssistantChange(selectedAssistant || null);
    }
  };

  const selectedAssistant = assistants.find(assistant => assistant.id === selectedId);

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium text-muted-foreground">
        Assistant
      </label>
      <Select value={selectedId} onValueChange={handleValueChange}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select an assistant">
            {selectedId === 'none' ? 'General Assistant' : selectedAssistant?.name}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="none">
            <div className="flex flex-col items-start">
              <div className="font-medium">General Assistant</div>
              <div className="text-xs text-muted-foreground">Default AI behavior</div>
            </div>
          </SelectItem>
          {assistants.map((assistant) => (
            <SelectItem key={assistant.id} value={assistant.id}>
              <div className="flex flex-col items-start">
                <div className="font-medium">{assistant.name}</div>
                <div className="text-xs text-muted-foreground">{assistant.description}</div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
