import type { Agent } from '../../../shared/agents/types';
import { storage } from '../../storage';

export const PersonalityAssessmentAgent: Agent = {
  name: 'Personality Assessment',
  description: 'Analyzes responses to determine personality traits',
  prompt: 'You are an expert personality assessment agent. Analyze user responses to determine their personality traits.',
  
  // Assessment questions with weights
  questions: [
    { id: 'q1', text: 'How do you typically react to unexpected changes?', weight: 1.0 },
    { id: 'q2', text: 'Do you prefer working in teams or independently?', weight: 1.2 },
    { id: 'q3', text: 'How do you handle criticism?', weight: 1.5 }
  ],
  
  // Process user responses and calculate scores
  processResponses: async (userId: number, responses: Record<string, string>) => {
    // Get user data from DB if needed
    const user = await storage.getUserById(userId);
    
    // Calculate scores based on responses and weights
    let extroversionScore = 0;
    let adaptabilityScore = 0;
    
    // Example scoring logic
    if (responses.q1.toLowerCase().includes('adapt quickly')) {
      adaptabilityScore += 5 * PersonalityAssessmentAgent.questions[0].weight;
    }
    
    // Store results in DB
    await storage.saveAssessmentResults(userId, {
      extroversion: extroversionScore,
      adaptability: adaptabilityScore
    });
    
    return {
      traits: {
        extroversion: extroversionScore > 15 ? 'high' : 'moderate',
        adaptability: adaptabilityScore > 10 ? 'high' : 'needs development'
      },
      recommendations: [
        extroversionScore > 15 ? 'Consider roles with high client interaction' : 'Consider roles with focused independent work',
        adaptabilityScore > 10 ? 'You would thrive in dynamic environments' : 'Consider developing adaptability skills'
      ]
    };
  }
};