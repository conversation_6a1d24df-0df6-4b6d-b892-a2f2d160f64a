import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { SampleAssistant } from '@/assistants/SampleAssistant';
import { CareerAssistant } from '@/assistants/CareerAssistant';
import { HealthAssistant } from '@/assistants/HealthAssistant';
import { useToast } from '@/components/ui/use-toast';

// Import all assistants
const assistants = [SampleAssistant, CareerAssistant, HealthAssistant];

export function AssistantSelector({ onSelect }: { onSelect: (prompt: string) => void }) {
  const [open, setOpen] = useState(false);
  const { toast } = useToast();

  const handleSelect = (prompt: string) => {
    onSelect(prompt);
    setOpen(false);
    toast({
      title: "Assistant changed",
      description: "Your messages will now be processed by the selected assistant",
    });
  };

  return (
    <>
      <Button variant="outline" onClick={() => setOpen(true)}>
        Change Assistant
      </Button>
      
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Select an Assistant</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {assistants.map((assistant) => (
              <Button 
                key={assistant.name}
                variant="outline" 
                className="justify-start h-auto py-3"
                onClick={() => handleSelect(assistant.prompt)}
              >
                <div className="text-left">
                  <div className="font-medium">{assistant.name}</div>
                  <div className="text-sm text-muted-foreground">{assistant.description}</div>
                </div>
              </Button>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}