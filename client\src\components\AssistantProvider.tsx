import { createContext, useContext, useState, ReactNode } from 'react';
import type { Assistant } from '../../../shared/assistants/types';

interface AssistantContextType {
  selectedAssistant: Assistant | null;
  setSelectedAssistant: (assistant: Assistant | null) => void;
}

const AssistantContext = createContext<AssistantContextType | undefined>(undefined);

export function useAssistant() {
  const context = useContext(AssistantContext);
  if (context === undefined) {
    throw new Error('useAssistant must be used within an AssistantProvider');
  }
  return context;
}

interface AssistantProviderProps {
  children: ReactNode;
}

export function AssistantProvider({ children }: AssistantProviderProps) {
  const [selectedAssistant, setSelectedAssistant] = useState<Assistant | null>(null);

  return (
    <AssistantContext.Provider value={{ selectedAssistant, setSelectedAssistant }}>
      {children}
    </AssistantContext.Provider>
  );
}
