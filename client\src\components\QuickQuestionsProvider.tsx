import { createContext, useContext, useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { UserSettings } from "@shared/schema";

type QuickQuestionsContextType = {
  showQuickQuestions: boolean;
};

const QuickQuestionsContext = createContext<QuickQuestionsContextType>({
  showQuickQuestions: false, // Default to hiding quick questions
});

export function useQuickQuestions() {
  return useContext(QuickQuestionsContext);
}

export function QuickQuestionsProvider({ children }: { children: React.ReactNode }) {
  const [showQuickQuestions, setShowQuickQuestions] = useState<boolean>(false); // Hide by default

  const settingsQuery = useQuery<UserSettings>({
    queryKey: ["/api/settings"],
  });

  useEffect(() => {
    if (settingsQuery.data) {
      setShowQuickQuestions(settingsQuery.data.showQuickQuestions ?? false);
    }
  }, [settingsQuery.data]);

  return (
    <QuickQuestionsContext.Provider value={{ showQuickQuestions }}>
      {children}
    </QuickQuestionsContext.Provider>
  );
}