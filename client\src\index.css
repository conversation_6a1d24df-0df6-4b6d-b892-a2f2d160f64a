@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 20 14.3% 4.1%;
  --muted: 60 4.8% 95.9%;
  --muted-foreground: 25 5.3% 44.7%;
  --popover: 0 0% 100%;
  --popover-foreground: 20 14.3% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 20 14.3% 4.1%;
  --border: 20 5.9% 90%;
  --input: 20 5.9% 90%;
  --primary: 160 84.1% 39.2%; /* ChatGPT Green */
  --primary-foreground: 211 100% 99%;
  --secondary: 240 5.9% 96.1%;
  --secondary-foreground: 24 9.8% 10%;
  --accent: 240 5.9% 96.1%;
  --accent-foreground: 24 9.8% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --ring: 160 84.1% 39.2%;
  --radius: 0.5rem;
  
  /* Custom chat colors */
  --user-msg: 160 50% 97%; /* Light green for user messages */
  --ai-msg: 220 14% 98%; /* Light gray for AI messages */
  --light-gray: 0 0% 97%;
  --dark-gray: 240 10% 4%;
  --sidebar-background: 0 0% 97%;
  --sidebar-foreground: 20 14.3% 4.1%;
  --sidebar-primary: 160 84.1% 39.2%;
  --sidebar-primary-foreground: 211 100% 99%;
  --sidebar-accent: 240 5.9% 96.1%;
  --sidebar-accent-foreground: 24 9.8% 10%;
  --sidebar-border: 20 5.9% 90%;
  --sidebar-ring: 160 84.1% 39.2%;
}

.dark {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --primary: 160 84.1% 39.2%; /* ChatGPT Green */
  --primary-foreground: 211 100% 99%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 240 4.9% 83.9%;
  
  /* Custom chat colors */
  --user-msg: 160 40% 20%; /* Dark green for user messages */
  --ai-msg: 240 5% 15%; /* Dark gray for AI messages */
  --light-gray: 0 0% 97%;
  --dark-gray: 240 10% 4%;
  --sidebar-background: 240 10% 10%;
  --sidebar-foreground: 0 0% 98%;
  --sidebar-primary: 160 84.1% 39.2%;
  --sidebar-primary-foreground: 211 100% 99%;
  --sidebar-accent: 240 3.7% 15.9%;
  --sidebar-accent-foreground: 0 0% 98%;
  --sidebar-border: 240 3.7% 15.9%;
  --sidebar-ring: 240 4.9% 83.9%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }

  /* Custom scrollbar styles */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}

/* Typing animation for typing indicator */
.typing-animation span {
  @apply w-2 h-2 bg-primary rounded-full inline-block mx-0.5;
  animation: typing 1.4s infinite ease-in-out both;
}

.typing-animation span:nth-child(1) {
  animation-delay: 0s;
}

.typing-animation span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-animation span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 80%, 100% { 
    transform: scale(0.6); 
    opacity: 0.6; 
  }
  40% { 
    transform: scale(1); 
    opacity: 1; 
  }
}

/* Text typing animation for AI responses */
.ai-typing-text {
  position: relative;
  white-space: pre-wrap;
  overflow: visible;
}

/* Inline cursor that appears next to the last character */
.ai-typing-cursor {
  display: inline;
  position: relative;
}

.ai-typing-cursor::after {
  content: '';
  position: absolute;
  right: -3px;
  top: 0;
  height: 100%;
  width: 2px;
  background-color: hsl(var(--primary));
  animation: typing-cursor 0.8s step-end infinite;
}

@keyframes typing-cursor {
  from, to { opacity: 0; }
  50% { opacity: 1; }
}

/* Markdown styling */
.markdown-content {
  @apply min-h-[1.5em]; /* Ensure consistent minimum height */
}

.markdown-content code {
  @apply bg-muted dark:bg-muted px-1 py-0.5 rounded text-sm font-mono;
}

.markdown-content pre {
  @apply bg-muted dark:bg-muted p-4 rounded-md my-2 overflow-x-auto;
}

/* Base styles for markdown content that apply to both typing and final states */
.markdown-content p {
  @apply mb-4 min-h-[1.5em]; /* Increased spacing between paragraphs */
}

.markdown-content ul,
.markdown-content ol {
  @apply pl-6 mb-4; /* Consistent left padding and bottom margin */
}

.markdown-content ul {
  @apply list-disc; /* Bullet points */
}

.markdown-content ol {
  @apply list-decimal; /* Numbered lists */
}

.markdown-content li {
  @apply mb-2; /* Spacing between list items */
}

.markdown-content h1, 
.markdown-content h2, 
.markdown-content h3, 
.markdown-content h4, 
.markdown-content h5, 
.markdown-content h6 {
  @apply font-bold mt-6 mb-4; /* Clear section headings */
}

.markdown-content h1 {
  @apply text-2xl;
}

.markdown-content h2 {
  @apply text-xl;
}

.markdown-content h3 {
  @apply text-lg;
}

.markdown-content a {
  @apply text-primary underline;
}

.markdown-content blockquote {
  @apply border-l-4 border-primary pl-4 italic my-2;
}

/* Message bubbles */
.user-message-bubble {
  @apply bg-[hsl(var(--user-msg))] dark:bg-[hsl(var(--user-msg))];
}

.ai-message-bubble {
  @apply bg-[hsl(var(--ai-msg))] dark:bg-[hsl(var(--ai-msg))];
}
