// Shared types for agents
export interface Agent {
  name: string;
  description: string;
  prompt: string;
  questions?: Array<{
    id: string;
    text: string;
    weight: number;
  }>;
  processResponses?: (userId: number, responses: Record<string, string>) => Promise<any>;
}

export interface AssessmentResult {
  userId: number;
  agentId: string;
  traits: Record<string, string>;
  recommendations: string[];
  createdAt: Date;
}

